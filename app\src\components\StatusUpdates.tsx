import React, { useEffect } from 'react';
import { ChatBubble } from './ChatBubble';
import type { StatusUpdatesProps } from '../types/ui';
import type { ChatMessage, AgentStatus, Message } from '../types/api';

/**
 * Status updates section with messenger-style chat
 */
export const StatusUpdates: React.FC<StatusUpdatesProps> = ({
  isPolling,
  statusUpdates,
  projectComplete,
}) => {
  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (isPolling && statusUpdates.length > 0) {
      const chatContainer = document.getElementById("chat-messages");
      if (chatContainer) {
        chatContainer.scrollTop = chatContainer.scrollHeight;
      }
    }
  }, [statusUpdates, isPolling]);

  if (!isPolling) {
    return null;
  }

  // Flatten and sort all messages from all agents
  const allMessages: ChatMessage[] = statusUpdates
    .flatMap((agent: AgentStatus) =>
      (agent.messages || []).map((msg: Message) => ({
        ...msg,
        agentId: agent.agentId,
        status: msg.status || agent.status,
      }))
    )
    .sort(
      (a: ChatMessage, b: ChatMessage) =>
        new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );

  return (
    <div id="status-updates" style={{ marginTop: 32 }}>
      <h2>Agent Chat</h2>
      <div
        id="chat-messages"
        style={{
          maxHeight: 360,
          overflowY: "auto",
          background: "#f8f8fa",
          borderRadius: 12,
          padding: 16,
          boxShadow: "0 2px 8px #0001",
          marginBottom: 8,
        }}
      >
        {statusUpdates.length > 0 ? (
          allMessages.map((msg, idx) => (
            <ChatBubble key={idx} message={msg} index={idx} />
          ))
        ) : (
          "Loading status messages..."
        )}
        
        {/* Progress loader at the bottom if polling */}
        {isPolling && !projectComplete && (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              marginTop: 8,
            }}
          >
            <div
              className="spinner"
              style={{ width: 28, height: 28, borderWidth: 4 }}
            />
            <span style={{ marginLeft: 8, color: "#888" }}>
              Agents are working...
            </span>
          </div>
        )}
      </div>
    </div>
  );
};
