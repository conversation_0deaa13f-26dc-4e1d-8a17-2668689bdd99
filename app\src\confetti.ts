import confetti from 'canvas-confetti';

export function triggerConfetti(): void {
  // Pick a random edge (0: top, 1: right, 2: bottom, 3: left)
  const edge = Math.floor(Math.random() * 4);
  
  // Set origin based on the selected edge
  let origin: { x: number; y: number };
  switch (edge) {
    case 0: // top
      origin = { x: Math.random(), y: 0 };
      break;
    case 1: // right
      origin = { x: 1, y: Math.random() };
      break;
    case 2: // bottom
      origin = { x: Math.random(), y: 1 };
      break;
    case 3: // left
      origin = { x: 0, y: Math.random() };
      break;
    default:
      origin = { x: 0.5, y: 0.5 };
  }
  
  // Calculate angle to aim toward center
  const angle = Math.atan2(0.5 - origin.y, 0.5 - origin.x);
  const angleDeg = angle * 180 / Math.PI;
  
  confetti({
    particleCount: 100,
    spread: 70,
    origin,
    angle: angleDeg,
    startVelocity: 30,
    gravity: 0.5,
    colors: ['#111', '#777', '#090', '#fff', '#eee'],
    shapes: ['square'],
    scalar: 1.2
  });
}