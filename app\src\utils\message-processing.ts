import type { WorkflowStreamEvent, AgentStatus, Message, ChatMessage } from '../types/api';

/**
 * Process workflow response data and extract relevant information
 */
export function processWorkflowResponse(rawData: { messages?: WorkflowStreamEvent[] }) {
  const dataObjects: WorkflowStreamEvent[] = rawData.messages ?? [];
  
  // Separate errors for special UI
  const errorObjects = dataObjects.filter((o) => o.status === "error");
  const mainObjects = dataObjects.filter(
    (o) => o.status !== "heartbeat" && o.status !== "error"
  );
  
  // Prefer status: 'completed', else last 'running', else first
  const feedbackObj =
    mainObjects.find((o) => o.status === "completed") ||
    [...mainObjects].reverse().find((o) => o.status === "running") ||
    mainObjects[0] ||
    dataObjects[0];

  return {
    dataObjects,
    errorObjects,
    mainObjects,
    feedbackObj,
  };
}

/**
 * Generate feedback message from workflow response
 */
export function generateFeedbackMessage(
  feedbackObj: WorkflowStreamEvent | undefined,
  errorObjects: WorkflowStreamEvent[]
): { message: string; color: string } {
  if (!feedbackObj) {
    return {
      message: "No response received from the workflow.",
      color: "#c00"
    };
  }

  const agentId = feedbackObj.agentId || "Unknown Agent";
  const message = feedbackObj.message || "";
  const executionId = feedbackObj.executionId || feedbackObj.timestamp || "N/A";
  const createdAt = feedbackObj.timestamp
    ? new Date(feedbackObj.timestamp).toLocaleString()
    : "now";
  const isActive = feedbackObj.status === "completed" || false;

  // Compose feedback message
  let feedbackMessage = `\n`;
  feedbackMessage += `🎬 Project Launch Sequence Initiated!\n`;
  feedbackMessage += `🧑‍💻 Agent: ${agentId} \n`;
  feedbackMessage += `🆔 Execution ID: ${executionId}\n`;
  feedbackMessage += `⏱️ Started: ${createdAt}\n`;
  feedbackMessage += `\n🤖 The Handsomest Nerd, Inc. multi-agent team is now assembling!\n`;
  feedbackMessage += `• Product Managers are sharpening their pencils...\n`;
  feedbackMessage += `• Developers are stretching their typing fingers...\n`;
  feedbackMessage += `• Designers are picking out their fanciest fonts...\n`;
  feedbackMessage += `• QA is already looking for bugs that don't exist yet!\n`;
  feedbackMessage += `\nHang tight — your project is officially in motion and the agents are collaborating to build your app.\n`;
  
  if (message) feedbackMessage += `\nStatus: ${message}\n`;
  
  // Optionally show output details if present
  if (feedbackObj.output && typeof feedbackObj.output === "object") {
    if (feedbackObj.output.workflowTrigger) {
      feedbackMessage += `\nWorkflow Triggered: ${feedbackObj.output.workflowTrigger.workflowId}\n`;
    }
    if (feedbackObj.output.workflowTrigger.outputContract) {
      feedbackMessage += `\nOutput Contract: ${JSON.stringify(
        feedbackObj.output.workflowTrigger.outputContract,
        null,
        2
      )}\n`;
    }
  }

  let color = isActive ? "#090" : "#f90";

  // Show error(s) in their own UI if present
  if (errorObjects.length > 0) {
    feedbackMessage += "\n\n🚨 <b>Workflow Error(s) Detected:</b>\n" +
      errorObjects
        .map(
          (err) =>
            `❌ <b>${err.agentId || "Unknown Agent"}</b>: ${
              err.message || "Unknown error"
            }`
        )
        .join("\n");
    color = "#c00";
  }

  return { message: feedbackMessage, color };
}

/**
 * Get all messages from all agents, sorted by timestamp
 */
export function getAllMessages(statusUpdates: AgentStatus[]): ChatMessage[] {
  const allMsgs: ChatMessage[] = [];
  
  statusUpdates.forEach((agent: AgentStatus) => {
    if (agent.messages && Array.isArray(agent.messages)) {
      agent.messages.forEach((msg: Message) => {
        allMsgs.push({
          ...msg,
          agentId: agent.agentId,
          agentName: agent.agentId, // Optionally map to friendly name
        });
      });
    }
  });

  // Sort by timestamp ascending
  return allMsgs.sort(
    (a, b) =>
      new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
  );
}

/**
 * Check for sandbox URL in agent messages
 */
export function findSandboxUrl(statusUpdates: AgentStatus[]): {
  foundSandboxUrl: string;
  foundCompleted: boolean;
} {
  let foundSandboxUrl = "";
  let foundCompleted = false;

  statusUpdates.forEach((agent: AgentStatus) => {
    agent.messages?.forEach((msg: Message) => {
      if (
        msg.message &&
        (msg.message.includes("codesandbox.io") ||
          msg.message.includes("sandbox"))
      ) {
        // Extract CodeSandbox URL if present
        const sandboxMatch = msg.message?.match(
          /(https:\/\/[\S]*codesandbox[\S]*)/i
        );
        if (sandboxMatch) {
          foundSandboxUrl = sandboxMatch[1];
        }
        foundCompleted = true;
      }
    });
  });

  return { foundSandboxUrl, foundCompleted };
}
