export interface ApiResponse {
  data: {
    totalCount: number;
    data: AgentStatus[];
  };
  success: boolean;
  message: string;
}

export interface AgentStatus {
  _id: string;
  agentId: string;
  currentStatus: CurrentStatus;
  messages: Message[];
  status: string;
}

export interface CurrentStatus {
  status: string;
  agentId: string;
  message: string;
  output: unknown; // Can be stringified JSON or object, so use any
  executionId: string;
  timestamp: string;
}

export interface Message {
  status: string;
  agentId: string;
  message: string;
  output?: unknown; // Can be object or string
  executionId: string;
  timestamp: string;
  data?: {
    stepIndex?: number | string;
    status?: string;
    isComplete?: boolean;
    workflowComplete?: boolean;
  };
  stepIndex?: string;
  isComplete?: boolean;
  workflowComplete?: boolean;
  parsedOutput?: unknown;
}

interface Output {
  workflowTrigger: {
    id: string;
    workflowId?: string;
    stepId?: string;
    active: boolean;
    triggeredBy: string;
    inputs: {
      inutType: string;
      systemPrompt: string;
      userPrompt: string;
    };
    outputContract?: {
      output: string;
      outputType: string;
      files?: unknown;
    };
    n8nId: string;
    workflowEndpoint: string;
    createdAt: string;
  };
}

// interface WorkflowLog {
//   workflowId: string;
//   triggeredBy: string;
//   workflowStatus: {
//     [key: string]: WorkflowStepStatus;
//   };
//   timestamp: string;
//   workflowUrl: string;
//   message: string;
//   userPrompt: string;
//   executionDetails: ExecutionDetails;
// }

// interface WorkflowStepStatus {
//   id: string;
//   workflowId: string;
//   stepId: string;
//   triggeredBy: string;
//   active: boolean;
//   inputs: WorkflowStepInputs;
//   n8nId: string;
//   workflowEndpoint: string;
//   createdAt: string;
//   output: {
//     revised_prompt?: string;
//     url?: string;
//   };
//   outputType: string;
//   completedAt: string;
// }

// interface WorkflowStepInputs {
//   inputType: string;
//   systemPrompt: string;
//   userPrompt: string;
// }

// interface ExecutionDetails {
//   startedAt: string;
//   completedAt: string;
//   executionTime: number;
//   status: string;
// }

// interface InputData {
//   executionId: string;
//   id: string;
//   n8nId: string;
//   stepNumber: string;
//   endpoint: string;
//   ownerAgentId: string;
//   body: {
//     character_head_description?: string;
//     character_name?: string;
//     expression?: string;
//     add_on_1?: string;
//     palette?: string;
//     add_on_2?: string;
//     add_on_3?: string;
//     action?: string;
//   };
// }

// interface FileContent {
//   content: string;
//   type: string;
// }

export interface WorkflowStreamEvent {
  // Required fields (present in all lines)
  agentId: string;
  timestamp: string;
  executionId: string;

  // Common/optional fields
  status?: string; // e.g. "running", "completed", "heartbeat"
  message?: string;
  output?: Output;
  data?: {
    stepIndex: number;
    status: string;
    isComplete: boolean;
    workflowComplete: boolean;
  };
  stepNumber?: string;
  // For heartbeat
  heartbeatCount?: number;
}
