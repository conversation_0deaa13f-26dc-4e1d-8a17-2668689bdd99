main {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: #fff;
}
h1 {
  font-size: 3rem;
  font-weight: bold;
  letter-spacing: -2px;
  margin-bottom: 2rem;
  background: #111;
  color: #fff;
  padding: 0.5em 1em;
  box-shadow: 8px 8px 0 #777;
  text-transform: uppercase;
}
form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  background: #eee;
  border: 4px solid #111;
  padding: 2rem 2.5rem;
  box-shadow: 8px 8px 0 #777;
  min-width: 320px;
  max-width: 90vw;

}
label {
  font-size: 1.2rem;
  font-weight: bold;
  text-transform: uppercase;
  color: #111;
  margin-bottom: 0.5rem;
}
textarea {
  font-size: 1.1rem;
  padding: 0.75em 1em;
  border: 3px solid #111;
  background: #fff;
  color: #111;
  outline: none;
  font-family:'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-weight: bolder;
  border-radius: 0;
  box-shadow: 4px 4px 0 #777;
}
textarea:focus {
  border-color: #000;
  background: #f5f5f5;
}
button {
  font-size: 2.2rem;
  font-weight: bold;
  background: #111;
  color: #fff;
  border: 3px solid #000;
  padding: 0.5em;
  cursor: pointer;
  text-transform: uppercase;
  box-shadow: 4px 4px 0 #777;
  transition: background 0.1s, color 0.1s;
  z-index: 1;
}
button:hover,
button:focus {
  background: #fff;
  color: #111;
}
button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}
#feedback {
  margin: 2rem;
  font-size: 1.1rem;
  font-weight: bold;
  min-height: 2em;
  background: #fff;
  border: 2px dashed #111;
  padding: 1em 1.5em;
  box-shadow: 4px 4px 0 #777;
  display: block;
  max-width: 800px;
  white-space: pre-line;
  text-align: left;
  line-height: 1.4;
}
.spinner {
  margin: 10px;
  border: 16px solid #f3f3f3;
  border-top: 16px solid #9a73c7;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  animation: spin 2s linear infinite;
}
.sandbox-url {
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@media (max-width: 600px) {
  h1 {
    font-size: 2rem;
    padding: 0.3em 0.5em;
  }
  form {
    padding: 1rem;
    min-width: 0;
  }
}

.spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 10px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #111;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.sandbox-url {
  display: block;
  margin-top: 10px;
  color: #0066cc;
  text-decoration: underline;
  font-weight: bold;
}

/* Status Updates Section - Brutalist Design */
#status-updates {
  margin: 2rem;
  background: #111;
  color: #fff;
  border: 4px solid #000;
  box-shadow: 8px 8px 0 #777;
  padding: 1.5rem;
  max-width: 800px;
}

#status-updates h2 {
  font-size: 1.5rem;
  font-weight: bold;
  text-transform: uppercase;
  margin: 0 0 1rem 0;
  background: #fff;
  color: #111;
  padding: 0.5rem 1rem;
  box-shadow: 4px 4px 0 #777;
  display: inline-block;
}

.status-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.status-item {
  background: #222;
  border: 2px solid #444;
  padding: 1rem;
  box-shadow: 4px 4px 0 #555;
  position: relative;
}

.status-item:first-child {
  border-color: #090;
  box-shadow: 4px 4px 0 #060;
}

.status-agent {
  font-size: 0.9rem;
  font-weight: bold;
  text-transform: uppercase;
  color: #ffd700;
  margin-bottom: 0.5rem;
}

.status-message {
  font-size: 1rem;
  line-height: 1.3;
  color: #fff;
  margin-bottom: 0.5rem;
}

.status-time {
  font-size: 0.8rem;
  color: #999;
  text-align: right;
  font-family: monospace;
}
