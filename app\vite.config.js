import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
// https://vitejs.dev/config/
export default defineConfig({
    plugins: [react()],
    server: {
        port: 5004,
        proxy: {
            '/api': {
                target: 'http://localhost:5678',
                changeOrigin: true,
                rewrite: function (path) { return path.replace(/^\/api/, ''); },
                configure: function (proxy) {
                    proxy.on('error', function (err) {
                        console.log('proxy error', err);
                    });
                }
            }
        }
    }
});
