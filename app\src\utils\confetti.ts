import type { ConfettiConfig } from '../types';

/**
 * Default confetti configuration
 */
const DEFAULT_CONFETTI_CONFIG: ConfettiConfig = {
  particleCount: 333,
  spread: 333,
  origin: { x: 0.5, y: 0.5 },
  startVelocity: 33,
  gravity: 0.3,
  ticks: 333,
  zIndex: 0,
  colors: ["#ffd", "#aec", "#fdd", "#e6f", "#fb0", "#ff7", "#acc"],
  shapes: ["circle", "square", "star"],
  scalar: 1.2,
};

/**
 * Calculate the origin position based on a button element
 * @param buttonId - The ID of the button element
 * @returns Origin coordinates for confetti
 */
export function calculateConfettiOrigin(buttonId: string): { x: number; y: number } {
  const button = document.getElementById(buttonId);
  const buttonRect = button?.getBoundingClientRect();
  
  if (buttonRect) {
    return {
      x: (buttonRect.left + buttonRect.right) / (2 * window.innerWidth),
      y: (buttonRect.top + buttonRect.bottom) / (2 * window.innerHeight),
    };
  }
  
  return { x: 0.5, y: 0.5 };
}

/**
 * Trigger confetti animation
 * @param confettiInstance - The confetti function from canvas-confetti
 * @param buttonId - Optional button ID to calculate origin from
 * @param customConfig - Optional custom configuration
 */
export function triggerConfetti(
  confettiInstance: any,
  buttonId?: string,
  customConfig?: Partial<ConfettiConfig>
): void {
  if (!confettiInstance) {
    console.warn('Confetti instance not available');
    return;
  }

  const origin = buttonId 
    ? calculateConfettiOrigin(buttonId)
    : DEFAULT_CONFETTI_CONFIG.origin;

  const config = {
    ...DEFAULT_CONFETTI_CONFIG,
    ...customConfig,
    origin,
  };

  confettiInstance(config);
}

/**
 * Load confetti dynamically
 * @returns Promise that resolves to the confetti function
 */
export async function loadConfetti(): Promise<any> {
  try {
    const module = await import('canvas-confetti');
    return module.default;
  } catch (error) {
    console.error('Failed to load confetti:', error);
    return null;
  }
}
