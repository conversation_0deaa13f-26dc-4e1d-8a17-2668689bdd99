import type { SWRConfiguration } from 'swr';
import { executeWorkflow, getAgentStatusUpdates } from './api';

/**
 * SWR fetcher for workflow execution
 */
export const workflowFetcher = async ([url, userPrompt]: [string, string]) => {
  // Extract workflow ID from URL
  const workflowId = url.split('id=')[1];
  if (!workflowId) {
    throw new Error('Invalid workflow URL - missing workflow ID');
  }

  // Generate execution ID (this should be passed from the component)
  const executionId = crypto.getRandomValues(new Uint8Array(8))
    .reduce((acc, byte) => acc + String.fromCharCode(byte), '');
  const base64Id = btoa(executionId).substring(0, 11);

  return executeWorkflow(workflowId, userPrompt, base64Id);
};

/**
 * SWR fetcher for status updates
 */
export const statusFetcher = async (executionId:string) => {
  // This should receive executionId as a parameter, but for now we'll extract from context
  // In a real implementation, this would be passed properly
  return getAgentStatusUpdates(executionId);
};

/**
 * Default SWR configuration
 */
const swrConfig: SWRConfiguration = {
  revalidateOnFocus: false,
  revalidateOnReconnect: false,
  shouldRetryOnError: false,
  errorRetryCount: 3,
  errorRetryInterval: 1000,
};

export default swrConfig;
