import React from 'react';
import type { ErrorBoundaryState } from '../types';

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error; errorInfo?: React.ErrorInfo }>;
}

/**
 * Error boundary component to catch and handle React errors
 */
export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.setState({ error, errorInfo });
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback;
        return (
          <FallbackComponent 
            error={this.state.error!} 
            errorInfo={this.state.errorInfo} 
          />
        );
      }

      return (
        <div style={{
          padding: '2rem',
          margin: '2rem',
          border: '4px solid #c00',
          background: '#fff',
          boxShadow: '8px 8px 0 #777',
          textAlign: 'center'
        }}>
          <h2 style={{
            color: '#c00',
            fontWeight: 'bold',
            textTransform: 'uppercase',
            marginBottom: '1rem'
          }}>
            Something went wrong!
          </h2>
          <p style={{ marginBottom: '1rem', color: '#111' }}>
            The application encountered an unexpected error. Please refresh the page and try again.
          </p>
          <button
            onClick={() => window.location.reload()}
            style={{
              background: '#c00',
              color: '#fff',
              border: '3px solid #900',
              padding: '0.5em 1em',
              fontWeight: 'bold',
              textTransform: 'uppercase',
              cursor: 'pointer',
              boxShadow: '4px 4px 0 #777'
            }}
          >
            Refresh Page
          </button>
          {process.env.NODE_ENV === 'development' && this.state.error && (
            <details style={{ marginTop: '1rem', textAlign: 'left' }}>
              <summary style={{ cursor: 'pointer', fontWeight: 'bold' }}>
                Error Details (Development Only)
              </summary>
              <pre style={{
                background: '#f5f5f5',
                padding: '1rem',
                overflow: 'auto',
                fontSize: '0.8rem',
                border: '1px solid #ddd'
              }}>
                {this.state.error.toString()}
                {this.state.errorInfo?.componentStack}
              </pre>
            </details>
          )}
        </div>
      );
    }

    return this.props.children;
  }
}
