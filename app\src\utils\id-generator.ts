/**
 * Generate a unique Base64 ID for execution tracking
 * @returns {string} An 11-character base64 string
 */
export function generateBase64Id(): string {
  const bytes = new Uint8Array(8); // 8 bytes will result in an 11-character base64 string
  crypto.getRandomValues(bytes); // Fills the array with random values
  
  // Convert the Uint8Array to a binary string
  let binaryString = "";
  for (let i = 0; i < bytes.length; i++) {
    binaryString += String.fromCharCode(bytes[i]);
  }
  
  // Encode the binary string to Base64
  const base64 = btoa(binaryString);
  
  // Take the first 11 characters
  return base64.substring(0, 11);
}

/**
 * Generate a simple UUID-like string
 * @returns {string} A UUID-like string
 */
export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}
