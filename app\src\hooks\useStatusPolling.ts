import { useState } from 'react';
import useSWR from 'swr';
import { findSandboxUrl } from '../utils';
import type { ApiResponse, AgentStatus } from '../types';

/**
 * Status fetcher function for polling
 */
const statusFetcher = async (url: string, executionId: string) => {
  const postBody = {
    limit: 5,
    executionId,
  };
  
  const res = await fetch(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Accept: "*/*",
    },
    credentials: "omit",
    body: JSON.stringify(postBody),
  });

  if (!res.ok) {
    throw new Error(`Status fetch failed: ${res.status}`);
  }
  
  return (await res.json()) as ApiResponse;
};

/**
 * Hook for managing status polling
 */
export function useStatusPolling(executionId: string) {
  const [statusUpdates, setStatusUpdates] = useState<AgentStatus[]>([]);
  const [isPolling, setIsPolling] = useState<boolean>(false);
  const [projectComplete, setProjectComplete] = useState<boolean>(false);
  const [sandboxUrl, setSandboxUrl] = useState<string>("");

  const statusUrl = isPolling && !projectComplete
    ? `${import.meta.env.VITE_GCLOUD_URL}/getAllAgentStatusUpdates`
    : null;

  const { error } = useSWR(
    statusUrl ? [statusUrl, executionId] : null,
    ([url, execId]) => statusFetcher(url, execId),
    {
      refreshInterval: 10000, // Poll every 10 seconds
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      onSuccess: ({ data }) => {
        const { data: agentData } = data;
        if (agentData) {
          setStatusUpdates(agentData);

          const { foundSandboxUrl, foundCompleted } = findSandboxUrl(agentData);

          if (foundCompleted) {
            if (foundSandboxUrl) {
              setSandboxUrl(foundSandboxUrl);
            }
            setProjectComplete(true);
            setIsPolling(false);
          }
        }
      },
      onError: (err) => {
        console.error("Status polling error:", err);
        // Don't show error to user for polling failures, just log them
      },
    }
  );

  const startPolling = () => {
    setIsPolling(true);
    setProjectComplete(false);
    setStatusUpdates([]);
    setSandboxUrl("");
  };

  const stopPolling = () => {
    setIsPolling(false);
  };

  const resetPolling = () => {
    setIsPolling(false);
    setProjectComplete(false);
    setStatusUpdates([]);
    setSandboxUrl("");
  };

  return {
    statusUpdates,
    isPolling,
    projectComplete,
    sandboxUrl,
    startPolling,
    stopPolling,
    resetPolling,
    error,
  };
}
