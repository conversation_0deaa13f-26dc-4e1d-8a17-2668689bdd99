export interface ApiResponse {
  data: {
    totalCount: number;
    data: AgentStatus[];
  };
  success: boolean;
  message: string;
}

export interface AgentStatus {
  _id: string;
  agentId: string;
  currentStatus: CurrentStatus;
  messages: Message[];
  status: string;
}

export interface CurrentStatus {
  status: string;
  agentId: string;
  message: string;
  output: unknown; // Can be stringified JSON or object, so use any
  executionId: string;
  timestamp: string;
}

export interface Message {
  status: string;
  agentId: string;
  message: string;
  output?: unknown; // Can be object or string
  executionId: string;
  timestamp: string;
  data?: {
    stepIndex?: number | string;
    status?: string;
    isComplete?: boolean;
    workflowComplete?: boolean;
  };
  stepIndex?: string;
  isComplete?: boolean;
  workflowComplete?: boolean;
  parsedOutput?: unknown;
}

export interface WorkflowStreamEvent {
  // Required fields (present in all lines)
  agentId: string;
  timestamp: string;
  executionId: string;

  // Common/optional fields
  status?: string; // e.g. "running", "completed", "heartbeat"
  message?: string;
  output?: Output;
  data?: {
    stepIndex: number;
    status: string;
    isComplete: boolean;
    workflowComplete: boolean;
  };
  stepNumber?: string;
  // For heartbeat
  heartbeatCount?: number;
}

export interface Output {
  workflowTrigger: {
    id: string;
    workflowId?: string;
    stepId?: string;
    active: boolean;
    triggeredBy: string;
    inputs: {
      inutType: string;
      systemPrompt: string;
      userPrompt: string;
    };
    outputContract?: {
      output: string;
      outputType: string;
      files?: unknown;
    };
    n8nId: string;
    workflowEndpoint: string;
    createdAt: string;
  };
}

// Extended message type for chat display
export interface ChatMessage extends Message {
  agentName?: string;
}

// Request/Response types for API calls
export interface WorkflowExecuteRequest {
  id: string;
  stepNumber: string;
  ownerAgentId: string;
  executionId: string;
  body: {
    userPrompt: string;
  };
}

export interface StatusRequest {
  limit: number;
  executionId: string;
  sinceDate?: string;
}

export interface WorkflowExecuteResponse {
  messages: WorkflowStreamEvent[];
}
