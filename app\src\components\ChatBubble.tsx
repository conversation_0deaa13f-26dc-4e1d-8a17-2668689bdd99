import React from 'react';
import type { ChatBubbleProps } from '../types';

/**
 * Individual chat bubble component for agent messages
 */
export const ChatBubble: React.FC<ChatBubbleProps> = ({ message, index }) => {
  const isCompass = message.agentId === "compass";
  
  return (
    <div
      key={index}
      className="chat-bubble"
      style={{
        display: "flex",
        alignItems: "flex-end",
        marginBottom: 12,
        flexDirection: isCompass ? "row-reverse" : "row",
      }}
    >
      {/* Avatar */}
      <div
        style={{
          width: 36,
          height: 36,
          borderRadius: "50%",
          background: isCompass ? "#e6f7ff" : "#eee",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          fontWeight: 700,
          fontSize: 18,
          marginLeft: isCompass ? 12 : 0,
          marginRight: isCompass ? 0 : 12,
          color: "#222",
        }}
        title={message.agentId}
      >
        {message.agentId?.[0]?.toUpperCase() || "?"}
      </div>
      
      {/* Message bubble */}
      <div
        style={{
          background: isCompass ? "#d1f7ff" : "#fff",
          borderRadius: 16,
          padding: "10px 16px",
          boxShadow: "0 1px 4px #0001",
          minWidth: 60,
          maxWidth: 320,
          wordBreak: "break-word",
          color: "#222",
        }}
      >
        <div
          style={{
            fontWeight: 600,
            fontSize: 13,
            color: "#555",
          }}
        >
          {message.agentId}
        </div>
        <div
          style={{
            fontSize: 15,
            margin: "4px 0",
            color: "#222",
          }}
        >
          {message.message || message.status || "Processing..."}
        </div>
        <div
          style={{
            fontSize: 11,
            color: "#888",
            textAlign: "right",
          }}
        >
          {message.timestamp
            ? new Date(message.timestamp).toLocaleTimeString()
            : ""}
        </div>
      </div>
    </div>
  );
};
