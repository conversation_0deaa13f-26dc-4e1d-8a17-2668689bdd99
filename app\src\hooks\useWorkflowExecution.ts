import { useState } from 'react';
import useSWR from 'swr';
import { generateBase64Id } from '../utils';
import { processWorkflowResponse, generateFeedbackMessage } from '../utils';
import { validatePrompt } from '../services';
import type { WorkflowExecuteResponse } from '../types';

/**
 * Custom fetcher for workflow execution
 */
const workflowFetcher = async ([url, userPrompt, executionId]: [string, string, string]) => {
  const workflowId = url.split('id=')[1];
  
  const bodyPost = {
    id: workflowId,
    stepNumber: "0",
    ownerAgentId: "compass",
    executionId,
    body: { userPrompt },
  };

  const res = await fetch(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Connection: "keep-alive",
      Accept: "*/*",
      "Content-Length": new Blob([JSON.stringify(bodyPost)]).size.toString(),
    },
    credentials: "omit",
    body: JSON.stringify(bodyPost),
  });

  if (!res.ok) {
    throw new Error(`Request failed: ${res.status}`);
  }
  
  return await res.json() as WorkflowExecuteResponse;
};

/**
 * Hook for managing workflow execution
 */
export function useWorkflowExecution() {
  const [requestKey, setRequestKey] = useState<string | null>(null);
  const [executionId] = useState(() => generateBase64Id());
  const [feedback, setFeedback] = useState<string>("");
  const [feedbackColor, setFeedbackColor] = useState<string>("#111");
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  const { error } = useSWR(
    requestKey ? [requestKey, requestKey.split('userPrompt=')[1], executionId] : null,
    workflowFetcher,
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      shouldRetryOnError: false,
      onSuccess: (rawData) => {
        const { errorObjects, feedbackObj } = processWorkflowResponse(rawData);
        const { message, color } = generateFeedbackMessage(feedbackObj, errorObjects);
        
        setFeedback(message);
        setFeedbackColor(color);
        setIsSubmitting(false);
        setRequestKey(null);
      },
      onError: (err) => {
        console.error(err);
        setFeedback(`The AI isn't AI-ing right now... Come back soon!`);
        setFeedbackColor("#c00");
        setIsSubmitting(false);
        setRequestKey(null);
      },
    }
  );

  const executeWorkflow = (prompt: string) => {
    const validation = validatePrompt(prompt);
    if (!validation.isValid) {
      setFeedback(validation.error || "Invalid prompt");
      setFeedbackColor("#c00");
      return false;
    }

    setIsSubmitting(true);
    setFeedback("Sending your request to the Handsomest Nerd Agents...");
    setFeedbackColor("#111");

    const baseUrl = `${import.meta.env.VITE_GCLOUD_URL}/executeWorkflow?id=sag_v0_dev`;
    setRequestKey(`${baseUrl}&userPrompt=${encodeURIComponent(prompt)}`);
    return true;
  };

  return {
    executeWorkflow,
    feedback,
    feedbackColor,
    isSubmitting,
    executionId,
    error,
  };
}
