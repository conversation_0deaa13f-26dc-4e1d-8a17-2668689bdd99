{"name": "n8n-k8s-frontend", "version": "1.0.0", "description": "Brutalist SPA for triggering n8n workflows + Kubernetes management scripts", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "k8s:backup-postgres": "node ../scripts/backup-postgres.js", "k8s:deploy-cloud-run": "bash ../scripts/deploy-cloud-run.sh", "k8s:fix-ingress": "bash ../scripts/fix-ingress.sh", "k8s:maintenance": "bash ../scripts/maintenance.sh", "k8s:n8n:deploy": "bash ../scripts/deploy-n8n.sh", "k8s:manage-cluster": "bash ../scripts/manage-cluster.sh"}, "dependencies": {"canvas-confetti": "^1.9.3", "dotenv": "^16.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "swr": "^2.3.3"}, "devDependencies": {"@types/canvas-confetti": "^1.6.4", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "serve": "^14.2.0", "typescript": "^5.3.3", "vite": "^5.1.0"}}