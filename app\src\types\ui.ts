// UI-specific types
export interface AppState {
  prompt: string;
  feedback: string;
  feedbackColor: string;
  isSubmitting: boolean;
  sandboxUrl: string;
  requestKey: string | null;
  statusUpdates: import('./api').AgentStatus[];
  isPolling: boolean;
  projectComplete: boolean;
}

export interface FormProps {
  prompt: string;
  isSubmitting: boolean;
  onPromptChange: (value: string) => void;
  onSubmit: (e: React.FormEvent<HTMLFormElement>) => void;
}

export interface FeedbackProps {
  feedback: string;
  feedbackColor: string;
  isSubmitting: boolean;
}

export interface StatusUpdatesProps {
  isPolling: boolean;
  statusUpdates: import('./api').AgentStatus[];
  projectComplete: boolean;
}

export interface AppError {
  message: string;
  type: 'network' | 'validation' | 'api' | 'unknown';
  details?: unknown;
}

export interface ChatBubbleProps {
  message: import('./api').ChatMessage;
  index: number;
}

export interface ConfettiConfig {
  particleCount: number;
  spread: number;
  origin: { x: number; y: number };
  startVelocity: number;
  gravity: number;
  ticks: number;
  zIndex: number;
  colors: string[];
  shapes: string[];
  scalar: number;
}

export interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}
