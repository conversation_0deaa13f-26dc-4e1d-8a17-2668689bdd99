import type { AppError } from '../types';

/**
 * Create a standardized error object
 */
export function createAppError(
  message: string,
  type: AppError['type'] = 'unknown',
  details?: unknown
): AppError {
  return {
    message,
    type,
    details,
  };
}

/**
 * Handle and format errors for user display
 */
export function formatErrorForUser(error: unknown): string {
  if (error instanceof Error) {
    // Handle specific error types
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      return 'Network error - please check your internet connection and try again.';
    }
    
    if (error.message.includes('404')) {
      return 'Service not found - the API endpoint may be unavailable.';
    }
    
    if (error.message.includes('500')) {
      return 'Server error - please try again in a few moments.';
    }
    
    if (error.message.includes('timeout')) {
      return 'Request timed out - please try again.';
    }
    
    return error.message || 'An unexpected error occurred.';
  }
  
  if (typeof error === 'string') {
    return error;
  }
  
  return 'An unexpected error occurred.';
}

/**
 * Log errors with context
 */
export function logError(error: unknown, context?: string): void {
  const timestamp = new Date().toISOString();
  const contextStr = context ? ` [${context}]` : '';
  
  console.error(`${timestamp}${contextStr}:`, error);
  
  // In production, you might want to send errors to a logging service
  if (import.meta.env.PROD) {
    // Example: sendToLoggingService(error, context);
  }
}

/**
 * Retry function with exponential backoff
 */
export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: unknown;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      if (attempt === maxRetries) {
        break;
      }
      
      const delay = baseDelay * Math.pow(2, attempt);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError;
}

/**
 * Safe async function wrapper that catches and logs errors
 */
export function safeAsync<T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  fallback?: R
): (...args: T) => Promise<R | undefined> {
  return async (...args: T) => {
    try {
      return await fn(...args);
    } catch (error) {
      logError(error, fn.name);
      return fallback;
    }
  };
}

/**
 * Validate environment variables
 */
export function validateEnvironment(): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (!import.meta.env.VITE_GCLOUD_URL) {
    errors.push('VITE_GCLOUD_URL environment variable is required');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}
