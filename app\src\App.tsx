import { useState, useEffect } from "react";
import "./App.css";
import { ErrorBoundary, PromptForm, FeedbackDisplay, StatusUpdates } from "./components";
import { useConfetti, useWorkflowExecution, useStatusPolling } from "./hooks";
import { validateEnvironment, logError } from "./utils/error-handling";

function App() {
  const [prompt, setPrompt] = useState<string>("");
  
  // Custom hooks for functionality
  const confetti = useConfetti();
  const {
    executeWorkflow,
    feedback,
    feedbackColor,
    isSubmitting,
    executionId,
  } = useWorkflowExecution();
  
  const {
    statusUpdates,
    isPolling,
    projectComplete,
    sandboxUrl,
    startPolling,
    resetPolling,
  } = useStatusPolling(executionId);

  // Validate environment on mount
  useEffect(() => {
    const { isValid, errors } = validateEnvironment();
    if (!isValid) {
      logError(`Environment validation failed: ${errors.join(', ')}`, 'App');
    }
  }, []);

  // Trigger confetti when workflow completes successfully
  useEffect(() => {
    if (projectComplete && sandboxUrl) {
      confetti.trigger('submit-button');
    }
  }, [projectComplete, sandboxUrl, confetti]);

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>): void => {
    e.preventDefault();
    const trimmedPrompt = prompt.trim();

    // Reset polling state when starting new project
    resetPolling();

    const success = executeWorkflow(trimmedPrompt);
    if (success) {
      confetti.trigger('submit-button');
      startPolling();
      setPrompt("");
    }
  };

  return (
    <ErrorBoundary>
      <main>
        <h1>Automatic App Generator</h1>
        <PromptForm
          prompt={prompt}
          isSubmitting={isSubmitting}
          onPromptChange={setPrompt}
          onSubmit={handleSubmit}
        />
        <FeedbackDisplay
          feedback={feedback}
          feedbackColor={feedbackColor}
          isSubmitting={isSubmitting}
        />
        <StatusUpdates
          isPolling={isPolling}
          statusUpdates={statusUpdates}
          projectComplete={projectComplete}
        />
      </main>
    </ErrorBoundary>
  );
}

export default App;
