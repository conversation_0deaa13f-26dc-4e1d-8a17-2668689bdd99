import { useState, useEffect } from "react";
import "./App.css";
import confetti from "canvas-confetti";
import useS<PERSON> from "swr";
import type {
  ApiResponse,
  AgentStatus,
  Message,
  CurrentStatus,
  WorkflowStreamEvent,
} from "./types";
// Custom fetcher function for SWR
function generateBase64Id(): string {
  const bytes = new Uint8Array(8); // 8 bytes will result in an 11-character base64 string (8 * 4 / 3 = 10.66, which rounds up to 11)
  crypto.getRandomValues(bytes); // Fills the array with random values
  // Convert the Uint8Array to a binary string
  let binaryString = "";
  for (let i = 0; i < bytes.length; i++) {
    binaryString += String.fromCharCode(bytes[i]);
  }
  // Encode the binary string to Base64
  const base64 = btoa(binaryString);
  // Take the first 11 characters
  return base64.substring(0, 11);
}
// Example usage:
const uniqueId = generateBase64Id();

type IResponse = { message: WorkflowStreamEvent[] };

const fetcher = async ([url, data]: [url: string, data: IResponse]) => {
  //capture the workflow id from the url from it's "workflowID" search parameter
  const workflowID = url.split("id=")[1];

  const bodyPost = {
    id: workflowID,
    stepNumber: "0",
    ownerAgentId: "compass",
    executionId: uniqueId,
    body: { userPrompt: data },
  };

  const res = await fetch(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Connection: "keep-alive",
      Accept: "*/*",
      "Content-Length": new Blob([JSON.stringify(data)]).size.toString(),
    },
    credentials: "omit",
    body: JSON.stringify(bodyPost),
  });

  if (!res.ok) {
    console.error("Request failed: " + res.status);
  }
  return await res.json();
};

// Status fetcher function for polling
const statusFetcher = async (url: string) => {
  const postBody = {
    limit: 5,
    // sinceDate: new Date(Date.now() - 60000).toISOString(),
    executionId: uniqueId,
  };
  const res = await fetch(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Accept: "*/*",
    },
    credentials: "omit",
    body: JSON.stringify(postBody),
  });

  if (!res.ok) {
    throw new Error(`Status fetch failed: ${res.status}`);
  }
  return (await res.json()) as ApiResponse;
};

function App() {
  const [prompt, setPrompt] = useState<string>("");
  const [feedback, setFeedback] = useState<string>("");
  const [feedbackColor, setFeedbackColor] = useState<string>("#111");
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [sandboxUrl, setSandboxUrl] = useState<string>("");
  const [confettiInstance, setConfettiInstance] = useState<
    typeof confetti | null
  >(null);
  const [requestKey, setRequestKey] = useState<string | null>(null);

  // New state for status polling
  const [statusUpdates, setStatusUpdates] = useState<AgentStatus[]>([]);
  const [isPolling, setIsPolling] = useState<boolean>(false);
  const [projectComplete, setProjectComplete] = useState<boolean>(false);

  // SWR hook for data fetching
  useSWR(requestKey ? [requestKey, prompt.trim()] : null, fetcher, {
    revalidateOnFocus: false,
    revalidateOnReconnect: false,
    shouldRetryOnError: false,
    onSuccess: (rawData) => {
      // Handle new response shape: { messages: WorkflowStreamEvent[] }
      const dataObjects: WorkflowStreamEvent[] = rawData.messages ?? [];
      console.log(dataObjects);
      // Separate errors for special UI
      const errorObjects = dataObjects.filter((o) => o.status === "error");
      const mainObjects = dataObjects.filter(
        (o) => o.status !== "heartbeat" && o.status !== "error"
      );
      // Prefer status: 'completed', else last 'running', else first
      const feedbackObj =
        mainObjects.find((o) => o.status === "completed") ||
        [...mainObjects].reverse().find((o) => o.status === "running") ||
        mainObjects[0] ||
        dataObjects[0];

      triggerConfetti();
      // Extract details for feedback
      const agentId = feedbackObj?.agentId || "Unknown Agent";
      const message = feedbackObj?.message || "";
      const executionId =
        feedbackObj?.executionId || feedbackObj?.timestamp || "N/A";
      const createdAt = feedbackObj?.timestamp
        ? new Date(feedbackObj.timestamp).toLocaleString()
        : "now";
      const isActive = feedbackObj?.status === "completed" || false;

      // Compose feedback message
      let feedbackMessage = `\n`;
      feedbackMessage += `🎬 Project Launch Sequence Initiated!\n`;
      feedbackMessage += `🧑‍💻 Agent: ${agentId} \n`;
      feedbackMessage += `🆔 Execution ID: ${executionId}\n`;
      feedbackMessage += `⏱️ Started: ${createdAt}\n`;
      feedbackMessage += `\n🤖 The Handsomest Nerd, Inc. multi-agent team is now assembling!\n`;
      feedbackMessage += `• Product Managers are sharpening their pencils...\n`;
      feedbackMessage += `• Developers are stretching their typing fingers...\n`;
      feedbackMessage += `• Designers are picking out their fanciest fonts...\n`;
      feedbackMessage += `• QA is already looking for bugs that don’t exist yet!\n`;
      feedbackMessage += `\nHang tight — your project is officially in motion and the agents are collaborating to build your app.\n`;
      if (message) feedbackMessage += `\nStatus: ${message}\n`;
      // Optionally show output details if present
      if (feedbackObj?.output && typeof feedbackObj.output === "object") {
        if (feedbackObj.output.workflowTrigger) {
          feedbackMessage += `\nWorkflow Triggered: ${feedbackObj.output.workflowTrigger.workflowId}\n`;
        }
        if (feedbackObj.output.workflowTrigger.outputContract) {
          feedbackMessage += `\nOutput Contract: ${JSON.stringify(
            feedbackObj.output.workflowTrigger.outputContract,
            null,
            2
          )}\n`;
        }
      }
      setFeedback(feedbackMessage);
      setFeedbackColor(isActive ? "#090" : "#f90");

      // Show error(s) in their own UI if present
      if (errorObjects.length > 0) {
        setFeedback(
          (prev) =>
            prev +
            "\n\n🚨 <b>Workflow Error(s) Detected:</b>\n" +
            errorObjects
              .map(
                (err) =>
                  `❌ <b>${err.agentId || "Unknown Agent"}</b>: ${
                    err.message || "Unknown error"
                  }`
              )
              .join("\n")
        );
        setFeedbackColor("#c00");
      }

      // Start polling if workflow is active and not completed
      if (isActive) {
        setIsPolling(true);
        setProjectComplete(false);
      }

      setPrompt("");
      setIsSubmitting(false);
      setRequestKey(null); // Reset the key after successful fetch
    },
    onError: (err) => {
      console.error(err);
      setFeedback(`The AI isn't AI-ing right now... Come back soon!`);
      setFeedbackColor("#c00");
      setIsSubmitting(false);
      setRequestKey(null); // Reset the key after error
    },
  });

  // SWR hook for status polling
  const statusUrl =
    isPolling && !projectComplete
      ? `${import.meta.env.VITE_GCLOUD_URL}/getAllAgentStatusUpdates`
      : null;
  useSWR(statusUrl, statusFetcher, {
    refreshInterval: 10000, // Poll every 5 seconds
    revalidateOnFocus: false,
    revalidateOnReconnect: false,
    onSuccess: ({ data, message }) => {
      const { data: agentData } = data;
      if (agentData) {
        setStatusUpdates(agentData);

        // Find a completed update/message with a codesandbox link or status
        let foundSandboxUrl = "";
        let foundCompleted = false;
        agentData.forEach((agent: AgentStatus) => {
          agent.messages?.forEach((msg: Message) => {
            console.log(message);
            if (
              msg.message &&
              (msg.message.includes("codesandbox.io") ||
                msg.message.includes("sandbox"))
            ) {
              // Extract CodeSandbox URL if present
              const sandboxMatch = msg.message?.match(
                /(https:\/\/[\S]*codesandbox[\S]*)/i
              );
              if (sandboxMatch) {
                foundSandboxUrl = sandboxMatch[1];
              }
              foundCompleted = true;
            }
          });
        });

        if (foundCompleted) {
          if (foundSandboxUrl) {
            setSandboxUrl(foundSandboxUrl);
            triggerConfetti();
            setFeedback(
              `🎉 Your application is ready!\n\nProject completed successfully!`
            );
            setFeedbackColor("#090");
          }
          setProjectComplete(true);
          setIsPolling(false);
        }
      }
    },
    onError: (err) => {
      console.error("Status polling error:", err);
      // Don't show error to user for polling failures, just log them
    },
  });

  // Dynamically import confetti on component mount
  useEffect(() => {
    import("canvas-confetti").then((module) => {
      setConfettiInstance(() => module.default);
    });
  }, []);

  // Function to trigger confetti
  const triggerConfetti = (): void => {
    if (!confettiInstance) return;

    // Calculate position of button
    const button = document.getElementById("submit-button");
    const buttonRect = button?.getBoundingClientRect();
    const origin = buttonRect
      ? {
          x: (buttonRect.left + buttonRect.right) / (2 * window.innerWidth),
          y: (buttonRect.top + buttonRect.bottom) / (2 * window.innerHeight),
        }
      : { x: 0.5, y: 0.5 };

    confettiInstance({
      particleCount: 333,
      spread: 333,
      origin,
      startVelocity: 33,
      gravity: 0.3,
      ticks: 333,
      zIndex: 0,
      colors: ["#ffd", "#aec", "#fdd", "#e6f", "#fb0", "#ff7", "#acc"],
      shapes: ["circle", "square", "star"],
      scalar: 1.2,
    });
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>): void => {
    e.preventDefault();
    const trimmedPrompt = prompt.trim();

    if (!trimmedPrompt) {
      setFeedback("Prompt cannot be empty.");
      setFeedbackColor("#c00");
      return;
    }

    // Reset polling state when starting new project
    setIsPolling(false);
    setProjectComplete(false);
    setStatusUpdates([]);
    setSandboxUrl("");

    setIsSubmitting(true);
    setFeedback("Sending your request to the Handsomest Nerd Agents...");
    setFeedbackColor("#111");

    // Set the request key to trigger SWR
    setRequestKey(
      `${import.meta.env.VITE_GCLOUD_URL}/executeWorkflow?id=sag_v0_dev`
    );
  };

  // Messenger-like status updates UI
  useEffect(() => {
    if (isPolling && statusUpdates.length > 0) {
      const chatContainer = document.getElementById("chat-messages");
      if (chatContainer) {
        chatContainer.scrollTop = chatContainer.scrollHeight;
      }
    }
  }, [statusUpdates, isPolling]);

  // Helper: get all messages from all agents, sorted by timestamp
  const getAllMessages = () => {
    const allMsgs: Message[] &
      { agentId?: string; agentName?: string; status?: string }[] = [];
    statusUpdates.forEach((agent: AgentStatus) => {
      if (agent.messages && Array.isArray(agent.messages)) {
        agent.messages.forEach((msg: Message) => {
          allMsgs.push({
            ...msg,
            agentId: agent.agentId,
            agentName: agent.agentId, // Optionally map to friendly name
            status: agent.status,
          });
        });
      }
    });
    console.log(allMsgs.length);
    // Sort by timestamp ascending
    return allMsgs.sort(
      (a, b) =>
        new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );
  };

  return (
    <main>
      <h1>Automatic App Generator</h1>
      <form id="promptForm" onSubmit={handleSubmit} autoComplete="off">
        <label htmlFor="prompt">Prompt</label>
        <textarea
          id="prompt"
          name="prompt"
          value={prompt}
          rows={10}
          onChange={(e) => setPrompt(e.target.value)}
          required
          autoFocus
          aria-label="Prompt input"
        />
        <button type="submit" disabled={isSubmitting} id="submit-button">
          🚀
        </button>
      </form>
      {(feedback || isSubmitting) && (
        <div
          id="feedback"
          role="status"
          aria-live="polite"
          style={{ color: feedbackColor }}
          dangerouslySetInnerHTML={{ __html: feedback }}
        />
      )}

      {/* Status Updates Section - Messenger Style */}
      {isPolling && (
        <div id="status-updates" style={{ marginTop: 32 }}>
          <h2>Agent Chat</h2>
          <div
            id="chat-messages"
            style={{
              maxHeight: 360,
              overflowY: "auto",
              background: "#f8f8fa",
              borderRadius: 12,
              padding: 16,
              boxShadow: "0 2px 8px #0001",
              marginBottom: 8,
            }}
          >
            {statusUpdates.length > 0
              ? statusUpdates
                  .flatMap((agent) =>
                    (agent.messages || []).map((msg) => ({
                      ...msg,
                      agentId: agent.agentId,
                      status: msg.status || agent.status,
                    }))
                  )
                  .sort(
                    (a, b) =>
                      new Date(a.timestamp).getTime() -
                      new Date(b.timestamp).getTime()
                  )
                  .map((msg, idx) => (
                    <div
                      key={idx}
                      className="chat-bubble"
                      style={{
                        display: "flex",
                        alignItems: "flex-end",
                        marginBottom: 12,
                        flexDirection:
                          msg.agentId === "compass" ? "row-reverse" : "row",
                      }}
                    >
                      {/* Avatar */}
                      <div
                        style={{
                          width: 36,
                          height: 36,
                          borderRadius: "50%",
                          background:
                            msg.agentId === "compass" ? "#e6f7ff" : "#eee",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          fontWeight: 700,
                          fontSize: 18,
                          marginLeft: msg.agentId === "compass" ? 12 : 0,
                          marginRight: msg.agentId === "compass" ? 0 : 12,
                          color: "#222",
                        }}
                        title={msg.agentId}
                      >
                        {msg.agentId?.[0]?.toUpperCase() || "?"}
                      </div>
                      {/* Message bubble */}
                      <div
                        style={{
                          background:
                            msg.agentId === "compass" ? "#d1f7ff" : "#fff",
                          borderRadius: 16,
                          padding: "10px 16px",
                          boxShadow: "0 1px 4px #0001",
                          minWidth: 60,
                          maxWidth: 320,
                          wordBreak: "break-word",
                          color: "#222",
                        }}
                      >
                        <div
                          style={{
                            fontWeight: 600,
                            fontSize: 13,
                            color: "#555",
                          }}
                        >
                          {msg.agentId}
                        </div>
                        <div
                          style={{
                            fontSize: 15,
                            margin: "4px 0",
                            color: "#222",
                          }}
                        >
                          {msg.message || msg.status || "Processing..."}
                        </div>
                        <div
                          style={{
                            fontSize: 11,
                            color: "#888",
                            textAlign: "right",
                          }}
                        >
                          {msg.timestamp
                            ? new Date(msg.timestamp).toLocaleTimeString()
                            : ""}
                        </div>
                      </div>
                    </div>
                  ))
              : "Loading status messages..."}
            {/* Progress loader at the bottom if polling */}
            {isPolling && !projectComplete && (
              <div
                style={{
                  display: "flex",
                  justifyContent: "center",
                  marginTop: 8,
                }}
              >
                <div
                  className="spinner"
                  style={{ width: 28, height: 28, borderWidth: 4 }}
                />
                <span style={{ marginLeft: 8, color: "#888" }}>
                  Agents are working...
                </span>
              </div>
            )}
          </div>
        </div>
      )}
    </main>
  );
}

export default App;
