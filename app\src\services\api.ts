import type {
  WorkflowExecuteRequest,
  WorkflowExecuteResponse,
  StatusRequest,
  ApiResponse,
} from '../types/api';
import type { AppError } from '../types/ui';

/**
 * Base API configuration
 */
const API_CONFIG = {
  baseUrl: import.meta.env.VITE_GCLOUD_URL,
  headers: {
    'Content-Type': 'application/json',
    'Connection': 'keep-alive',
    'Accept': '*/*',
  },
  credentials: 'omit' as RequestCredentials,
};

/**
 * Custom error class for API errors
 */
export class ApiError extends Error implements AppError {
  type: AppError['type'];
  details?: unknown;

  constructor(message: string, type: AppError['type'] = 'api', details?: unknown) {
    super(message);
    this.name = 'ApiError';
    this.type = type;
    this.details = details;
  }
}

/**
 * Generic fetch wrapper with error handling
 */
async function fetchWithErrorHandling<T>(
  url: string,
  options: RequestInit
): Promise<T> {
  try {
    const response = await fetch(url, {
      ...options,
      headers: {
        ...API_CONFIG.headers,
        ...options.headers,
      },
      credentials: API_CONFIG.credentials,
    });

    if (!response.ok) {
      throw new ApiError(
        `Request failed: ${response.status} ${response.statusText}`,
        'network',
        { status: response.status, statusText: response.statusText }
      );
    }

    return await response.json();
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    
    if (error instanceof TypeError && error.message.includes('fetch')) {
      throw new ApiError('Network error - please check your connection', 'network', error);
    }
    
    throw new ApiError('An unexpected error occurred', 'unknown', error);
  }
}

/**
 * Execute workflow with n8n
 */
export async function executeWorkflow(
  workflowId: string,
  userPrompt: string,
  executionId: string
): Promise<WorkflowExecuteResponse> {
  const url = `${API_CONFIG.baseUrl}/executeWorkflow?id=${workflowId}`;
  
  const requestBody: WorkflowExecuteRequest = {
    id: workflowId,
    stepNumber: '0',
    ownerAgentId: 'compass',
    executionId,
    body: { userPrompt },
  };

  const bodyString = JSON.stringify(requestBody);
  
  return fetchWithErrorHandling<WorkflowExecuteResponse>(url, {
    method: 'POST',
    headers: {
      'Content-Length': new Blob([bodyString]).size.toString(),
    },
    body: bodyString,
  });
}

/**
 * Get agent status updates
 */
export async function getAgentStatusUpdates(
  executionId: string,
  limit: number = 5
): Promise<ApiResponse> {
  const url = `${API_CONFIG.baseUrl}/getAllAgentStatusUpdates`;
  
  const requestBody: StatusRequest = {
    limit,
    executionId,
  };

  return fetchWithErrorHandling<ApiResponse>(url, {
    method: 'POST',
    body: JSON.stringify(requestBody),
  });
}

/**
 * Validate user input
 */
export function validatePrompt(prompt: string): { isValid: boolean; error?: string } {
  const trimmed = prompt.trim();
  
  if (!trimmed) {
    return { isValid: false, error: 'Prompt cannot be empty.' };
  }
  
  if (trimmed.length > 10000) {
    return { isValid: false, error: 'Prompt is too long. Please keep it under 10,000 characters.' };
  }
  
  return { isValid: true };
}
