import React from 'react';
import type { FormProps } from '../types/ui';

/**
 * Form component for user prompt input
 */
export const PromptForm: React.FC<FormProps> = ({
  prompt,
  isSubmitting,
  onPromptChange,
  onSubmit,
}) => {
  return (
    <form id="promptForm" onSubmit={onSubmit} autoComplete="off">
      <label htmlFor="prompt">Prompt</label>
      <textarea
        id="prompt"
        name="prompt"
        value={prompt}
        rows={10}
        onChange={(e) => onPromptChange(e.target.value)}
        required
        autoFocus
        aria-label="Prompt input"
      />
      <button type="submit" disabled={isSubmitting} id="submit-button">
        🚀
      </button>
    </form>
  );
};
