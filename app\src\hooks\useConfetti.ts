import { useState, useEffect } from 'react';
import { loadConfetti, triggerConfetti } from '../utils';
import type { ConfettiConfig } from '../types/ui';

/**
 * Hook for managing confetti functionality
 */
export function useConfetti() {
  const [confettiInstance, setConfettiInstance] = useState<any>(null);

  // Load confetti on mount
  useEffect(() => {
    loadConfetti().then((confetti) => {
      if (confetti) {
        setConfettiInstance(() => confetti);
      }
    });
  }, []);

  const trigger = (buttonId?: string, customConfig?: Partial<ConfettiConfig>) => {
    triggerConfetti(confettiInstance, buttonId, customConfig);
  };

  return {
    trigger,
    isLoaded: !!confettiInstance,
  };
}
