# n8n Brutalist React Trigger

A brutalist-styled React application for triggering n8n workflows via webhook.

## Features
- Brutalist, high-contrast, accessible design
- Responsive layout
- Input form for prompt
- POSTs to n8n webhook endpoint
- Success/error feedback
- Environment variables for webhook URL configuration

## Environment Configuration
- Edit `.env.development` or `.env.production` to set the webhook URL for your environment:
  - For local dev: `VITE_N8N_WEBHOOK_URL=http://localhost:5678/webhook-test/your-id`
  - For production: `VITE_N8N_WEBHOOK_URL=https://n8n.tenksolutions.com/webhook/your-id`

## Development
1. Install dependencies:
   ```
   npm install
   ```
2. Start the development server:
   ```
   npm run dev
   ```

## Building for Production
1. Build the application:
   ```
   npm run build
   ```
2. Preview the production build:
   ```
   npm run preview
   ```
3. Deploy the contents of the `/dist` directory to any static web host (Netlify, Vercel, GCS, etc.)

## Accessibility
- Keyboard accessible
- ARIA feedback for status

## Customization
- Edit `src/App.css` for design tweaks
- Edit `src/App.jsx` for logic changes

---
MIT License