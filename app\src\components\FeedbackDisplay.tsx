import React from 'react';
import type { FeedbackProps } from '../types/ui';

/**
 * Component to display feedback messages to the user
 */
export const FeedbackDisplay: React.FC<FeedbackProps> = ({
  feedback,
  feedbackColor,
  isSubmitting,
}) => {
  if (!feedback && !isSubmitting) {
    return null;
  }

  return (
    <div
      id="feedback"
      role="status"
      aria-live="polite"
      style={{ color: feedbackColor }}
      dangerouslySetInnerHTML={{ __html: feedback }}
    />
  );
};
